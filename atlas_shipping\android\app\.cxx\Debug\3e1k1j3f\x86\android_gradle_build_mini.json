{"buildFiles": ["C:\\Users\\<USER>\\Documents\\Flutter Is Here\\flutter_windows_3.22.3-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Flutter Apps\\AtlasShipping\\atlas_shipping\\android\\app\\.cxx\\Debug\\3e1k1j3f\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\Flutter Apps\\AtlasShipping\\atlas_shipping\\android\\app\\.cxx\\Debug\\3e1k1j3f\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}