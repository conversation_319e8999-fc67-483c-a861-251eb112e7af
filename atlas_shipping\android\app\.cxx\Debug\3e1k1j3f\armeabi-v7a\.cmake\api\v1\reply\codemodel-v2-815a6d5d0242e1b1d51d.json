{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/Flutter Apps/AtlasShipping/atlas_shipping/android/app/.cxx/Debug/3e1k1j3f/armeabi-v7a", "source": "C:/Users/<USER>/Documents/Flutter Is Here/flutter_windows_3.22.3-stable/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}